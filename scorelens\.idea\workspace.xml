<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2e0c6577-1932-473a-a07e-4bce83f0ed16" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/scorelens/Config/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/scorelens/Config/SecurityConfig.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Enum" />
        <option value="com.intellij.jpb.JpaRepository" />
        <option value="com.intellij.jpb.NewClass" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/.." value="tuyendepchai" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="JpbToolWindowState">
    <option name="isToolWindowVisible" value="false" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2xu1U5O1pOWjJ7chUqNbpZIpwmh" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "DtoCreateDialog.EQUALS_HASH_CODE_INITIAL_PROP_KEY": "false",
    "DtoCreateDialog.TO_STRING_INITIAL_PROP_KEY": "false",
    "JUnit.CustomerControllerTest.executor": "Run",
    "JUnit.CustomerControllerTest.testDeleteCustomer.executor": "Run",
    "JUnit.CustomerControllerTest.testGetAllCustomers.executor": "Run",
    "JUnit.CustomerControllerTest.testGetCustomerById.executor": "Run",
    "JUnit.ScorelensApplicationTests.executor": "Run",
    "JUnit.StaffControllerMockTest.executor": "Run",
    "JUnit.StaffControllerMockTest.testUpdateStaffWithStore.executor": "Run",
    "Maven.scorelens [clean].executor": "Run",
    "Maven.scorelens [compile].executor": "Run",
    "Maven.scorelens [install].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.ScorelensApplication.executor": "Run",
    "git-widget-placeholder": "features",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Fen/Quan/SWD392_BE_MOBILE",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Libraries",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "reference.projectsettings.compiler.annotationProcessors",
    "settings.editor.splitter.proportion": "0.26872537",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="com.scorelens.DTOs.Request" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\FPTU\S7\SWD\BE\scorelens\src\main\resources\db\migration" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.scorelens.Service" />
      <recent name="com.scorelens.Controller.v2" />
      <recent name="com.scorelens.DTOs.Response" />
      <recent name="com.scorelens.DTOs.Request" />
      <recent name="com.scorelens.Controller" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.ScorelensApplication">
    <configuration name="CustomerControllerTest.testDeleteCustomer" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="scorelens" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.scorelens.Controller.v1.AuthenticationController" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.scorelens.Controller" />
      <option name="MAIN_CLASS_NAME" value="com.scorelens.Controller.CustomerControllerTest" />
      <option name="METHOD_NAME" value="testDeleteCustomer" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CustomerControllerTest.testGetAllCustomers" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="scorelens" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.scorelens.Controller.v1.AuthenticationController" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.scorelens.Controller" />
      <option name="MAIN_CLASS_NAME" value="com.scorelens.Controller.CustomerControllerTest" />
      <option name="METHOD_NAME" value="testGetAllCustomers" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CustomerControllerTest.testGetCustomerById" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="scorelens" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.scorelens.Controller.v1.AuthenticationController" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.scorelens.Controller" />
      <option name="MAIN_CLASS_NAME" value="com.scorelens.Controller.CustomerControllerTest" />
      <option name="METHOD_NAME" value="testGetCustomerById" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ScorelensApplicationTests" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="scorelens" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.scorelens.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.scorelens" />
      <option name="MAIN_CLASS_NAME" value="com.scorelens.ScorelensApplicationTests" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="StaffControllerMockTest.testUpdateStaffWithStore" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="scorelens" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.scorelens.Controller.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.scorelens.Controller" />
      <option name="MAIN_CLASS_NAME" value="com.scorelens.Controller.StaffControllerMockTest" />
      <option name="METHOD_NAME" value="testUpdateStaffWithStore" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ScorelensApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="scorelens" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.scorelens.ScorelensApplication" />
      <option name="VM_PARAMETERS" value="--enable-native-access=ALL-UNNAMED " />
      <extension name="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.CustomerControllerTest.testGetCustomerById" />
        <item itemvalue="JUnit.CustomerControllerTest.testGetAllCustomers" />
        <item itemvalue="JUnit.CustomerControllerTest.testDeleteCustomer" />
        <item itemvalue="JUnit.ScorelensApplicationTests" />
        <item itemvalue="JUnit.StaffControllerMockTest.testUpdateStaffWithStore" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-b114ca120d71-intellij.indexing.shared.core-IU-242.21829.142" />
        <option value="bundled-js-predefined-d6986cc7102b-7c0b70fcd90d-JavaScript-IU-242.21829.142" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2e0c6577-1932-473a-a07e-4bce83f0ed16" name="Changes" comment="" />
      <created>1748773156740</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748773156740</updated>
      <workItem from="1748773157833" duration="304000" />
      <workItem from="1748773470415" duration="4980000" />
      <workItem from="1748785589734" duration="7834000" />
      <workItem from="1748793534091" duration="2933000" />
      <workItem from="1748796484405" duration="954000" />
      <workItem from="1748826628432" duration="6515000" />
      <workItem from="1748843948428" duration="5865000" />
      <workItem from="1748851237045" duration="5980000" />
      <workItem from="1748858499662" duration="609000" />
      <workItem from="1748863202831" duration="5378000" />
      <workItem from="1748873326217" duration="5261000" />
      <workItem from="1748879357373" duration="2589000" />
      <workItem from="1748919074000" duration="7000" />
      <workItem from="1748919087584" duration="6027000" />
      <workItem from="1748937395227" duration="13836000" />
      <workItem from="1748953620998" duration="1201000" />
      <workItem from="1748961355378" duration="6137000" />
      <workItem from="1749024315734" duration="1209000" />
      <workItem from="1749034448365" duration="5391000" />
      <workItem from="1749053611064" duration="4335000" />
      <workItem from="1749058186640" duration="2929000" />
      <workItem from="1749061258441" duration="422000" />
      <workItem from="1749089040233" duration="322000" />
      <workItem from="1749089372691" duration="307000" />
      <workItem from="1749089689688" duration="107000" />
      <workItem from="1749089804211" duration="251000" />
      <workItem from="1749090064600" duration="150000" />
      <workItem from="1749090223822" duration="153000" />
      <workItem from="1749091369690" duration="19000" />
      <workItem from="1749103942524" duration="5000" />
      <workItem from="1749105569169" duration="1698000" />
      <workItem from="1749113885507" duration="1053000" />
      <workItem from="1749127964026" duration="3756000" />
      <workItem from="1749140192023" duration="2805000" />
      <workItem from="1749144150789" duration="2032000" />
      <workItem from="1749172462649" duration="316000" />
      <workItem from="1749172795519" duration="7070000" />
      <workItem from="1749179998609" duration="304000" />
      <workItem from="1749180312903" duration="620000" />
      <workItem from="1749181031972" duration="6357000" />
      <workItem from="1749195722386" duration="2572000" />
      <workItem from="1749198338373" duration="316000" />
      <workItem from="1749204667294" duration="9423000" />
      <workItem from="1749217973099" duration="34000" />
      <workItem from="1749218015117" duration="8413000" />
      <workItem from="1749266770225" duration="4750000" />
      <workItem from="1749310138588" duration="2628000" />
      <workItem from="1749459285675" duration="94000" />
      <workItem from="1749478705796" duration="912000" />
      <workItem from="1749525239757" duration="3794000" />
      <workItem from="1749536912959" duration="2198000" />
      <workItem from="1749539791481" duration="1218000" />
      <workItem from="1749566550679" duration="3277000" />
      <workItem from="1749608076594" duration="15592000" />
      <workItem from="1749639691980" duration="4578000" />
      <workItem from="1749646717191" duration="8463000" />
      <workItem from="1749691689272" duration="9046000" />
      <workItem from="1749708730055" duration="771000" />
      <workItem from="1749716941421" duration="1636000" />
      <workItem from="1749721089879" duration="77000" />
      <workItem from="1749721244874" duration="104000" />
      <workItem from="1749741211722" duration="6649000" />
      <workItem from="1749749544721" duration="392000" />
      <workItem from="1749785013776" duration="3979000" />
      <workItem from="1749789767655" duration="2830000" />
      <workItem from="1749794528693" duration="51000" />
      <workItem from="1749794591441" duration="5452000" />
      <workItem from="1749886048770" duration="15000" />
      <workItem from="1749886275719" duration="2121000" />
      <workItem from="1749900146367" duration="23000" />
      <workItem from="1749963173530" duration="848000" />
      <workItem from="1749964579887" duration="1471000" />
      <workItem from="1750000545384" duration="1719000" />
      <workItem from="1750002765929" duration="41000" />
      <workItem from="1750038743408" duration="8157000" />
      <workItem from="1750061619462" duration="850000" />
      <workItem from="1750065651882" duration="505000" />
      <workItem from="1750129838179" duration="7306000" />
      <workItem from="1750153406894" duration="1023000" />
      <workItem from="1750154761047" duration="204000" />
      <workItem from="1750178232218" duration="442000" />
      <workItem from="1750238708568" duration="2124000" />
      <workItem from="1750255618690" duration="700000" />
      <workItem from="1750299095585" duration="1569000" />
      <workItem from="1750300725215" duration="11000" />
      <workItem from="1750304133660" duration="298000" />
      <workItem from="1750315328466" duration="4285000" />
      <workItem from="1750320599275" duration="2553000" />
      <workItem from="1750324910925" duration="15000" />
      <workItem from="1750324989248" duration="1726000" />
      <workItem from="1750327081291" duration="1250000" />
      <workItem from="1750334190345" duration="24000" />
      <workItem from="1750468044063" duration="453000" />
      <workItem from="1750468633092" duration="1679000" />
      <workItem from="1750470689781" duration="25807000" />
      <workItem from="1750508768247" duration="554000" />
      <workItem from="1750510682416" duration="3562000" />
      <workItem from="1750557979489" duration="1263000" />
      <workItem from="1750559263182" duration="22000" />
      <workItem from="1750683435600" duration="4868000" />
      <workItem from="1750732489390" duration="420000" />
      <workItem from="1750734322763" duration="5641000" />
      <workItem from="1750749911122" duration="3756000" />
      <workItem from="1750755595950" duration="4996000" />
      <workItem from="1750775898106" duration="2941000" />
      <workItem from="1750779339389" duration="166000" />
      <workItem from="1750813860388" duration="485000" />
      <workItem from="1750814375668" duration="3000" />
      <workItem from="1750817479226" duration="4672000" />
      <workItem from="1750841353620" duration="4553000" />
      <workItem from="1750848277122" duration="2987000" />
      <workItem from="1750851751971" duration="88000" />
      <workItem from="1750904424978" duration="16000" />
      <workItem from="1750909076331" duration="2000" />
      <workItem from="1750909770226" duration="20000" />
      <workItem from="1750910382304" duration="9000" />
      <workItem from="1750910652041" duration="84000" />
      <workItem from="1750911616922" duration="1185000" />
      <workItem from="1750997852509" duration="1275000" />
      <workItem from="1751003067390" duration="371000" />
      <workItem from="1751013057342" duration="1101000" />
      <workItem from="1751016989560" duration="175000" />
      <workItem from="1751017739979" duration="975000" />
      <workItem from="1751027438171" duration="1067000" />
      <workItem from="1751037302459" duration="438000" />
      <workItem from="1751092836444" duration="325000" />
      <workItem from="1751097979270" duration="64000" />
      <workItem from="1751106103308" duration="4000" />
      <workItem from="1751107371283" duration="303000" />
      <workItem from="1751109809053" duration="693000" />
      <workItem from="1751190182237" duration="1075000" />
      <workItem from="1751194309250" duration="2727000" />
      <workItem from="1751203069947" duration="3306000" />
      <workItem from="1751275283378" duration="1000" />
      <workItem from="1751275886581" duration="714000" />
      <workItem from="1751339877303" duration="506000" />
      <workItem from="1751341067814" duration="5671000" />
      <workItem from="1751429341473" duration="734000" />
      <workItem from="1751520298106" duration="25000" />
      <workItem from="1751555505081" duration="1615000" />
      <workItem from="1751557356414" duration="540000" />
      <workItem from="1751559059750" duration="575000" />
      <workItem from="1751560656952" duration="1528000" />
      <workItem from="1751597050390" duration="1961000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>