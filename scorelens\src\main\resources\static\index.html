<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>WebSocket Multi-Topic Demo</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        #messages {
            margin-top: 20px;
        }

        .message {
            padding: 5px 0;
        }

        .notification {
            color: green;
        }

        .logging {
            color: red;
        }
    </style>
</head>
<body>

<h1>WebSocket</h1>

<input type="text" id="messageInput" placeholder="Type a message..."/>
<button onclick="sendNotification()">Send Notification</button>
<button onclick="sendLogging()">Send Logging</button>

<div id="messages"></div>

<h3>Shot History</h3>
<table border="1">
    <thead>
    <tr>
        <th>Time</th>
        <th>Shot</th>
        <th>Player</th>
        <th>Result</th>
    </tr>
    </thead>
    <tbody id="shotTableBody"></tbody>
</table>

<script>
    let stompClient = null;

    function connect() {
        const socket = new SockJS('https://scorelens.onrender.com/ws');
        // const socket = new SockJS('http://localhost:8080/ws');
        stompClient = Stomp.over(socket);

        stompClient.connect({}, function (frame) {
            console.log('Connected: ' + frame);

            // Subscribe topic /topic/notification
            stompClient.subscribe('/topic/notification/23374e21-2391-41b0-b275-651df88b3b04', function (messageOutput) {
                showMessage(messageOutput.body, 'notification');
            });

            // Subscribe topic /topic/logging_notification
            stompClient.subscribe('/topic/logging_notification/23374e21-2391-41b0-b275-651df88b3b04', function (messageOutput) {
                showMessage(messageOutput.body, 'logging');
            });

            // Subscribe topic /topic/shot_event
            stompClient.subscribe('/topic/shot_event/23374e21-2391-41b0-b275-651df88b3b04', function (messageOutput) {
                const data = JSON.parse(messageOutput.body);
                showShotEvent(data);
            });

        });
    }

    function sendNotification() {
        const messageInput = document.getElementById('messageInput');
        const message = messageInput.value.trim();

        if (message && stompClient) {
            stompClient.send("/app/noti.send", {}, message);
            messageInput.value = '';
        }
    }

    function sendLogging() {
        const messageInput = document.getElementById('messageInput');
        const message = messageInput.value.trim();

        if (message && stompClient) {
            stompClient.send("/app/log.send", {}, message);
            messageInput.value = '';
        }
    }

    function showMessage(message, type) {
        const messagesDiv = document.getElementById('messages');
        const messageElement = document.createElement('div');
        messageElement.className = 'message ' + type;
        messageElement.textContent = `[${type.toUpperCase()}] ${message}`;
        messagesDiv.appendChild(messageElement);
    }

    function showShotEvent(data) {
        const tableBody = document.getElementById('shotTableBody');
        const row = document.createElement('tr');

        row.innerHTML = `
        <td>${data.time}</td>
        <td>${data.shot}</td>
        <td>${data.player}</td>
        <td style="color: ${data.result === 'SCORED' ? 'green' : 'red'}">${data.result}</td>
    `;

        tableBody.appendChild(row);
    }


    connect();
</script>

</body>
</html>
