server:
  port: 8080
  servlet:
    context-path: /
spring:
  datasource:
    url: ${DB_URL}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
  web:
    resources:
      add-mappings: true
  config:
    import: optional:file:.env[.properties]
  jpa:
    hibernate:
      ddl-auto: update
    #      naming:
    #        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
  kafka:
    ssl:
      key-store-location: ${KAFKA_KEYSTORE_LOCATION}
      key-store-password: ${KAFKA_KEYSTORE_PASSWORD}
      key-store-type: ${KAFKA_KEYSTORE_TYPE}
      key-password: ${KAFKA_KEY_PASSWORD}
      trust-store-location: ${KAFKA_TRUSTSTORE_LOCATION}
      trust-store-password: ${KAFKA_TRUSTSTORE_PASSWORD}
      trust-store-type: ${KAFKA_TRUSTSTORE_TYPE}
    consumer:
      group-id: ${KAFKA_GROUP_ID}
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    # Producer config
    producer:
      retries: 3                             # Retry 3 lần nếu lỗi
      properties:
        retry.backoff.ms: 1000               # Thời gian giữa các lần retry
        connections.max.idle.ms: 30000      # Idle connection timeout
        max.block.ms: 5000                   # Block tối đa khi Kafka chưa sẵn sàng
        request.timeout.ms: 3000             # Thời gian chờ broker trả lời
      acks: all                               # Confirm tất cả broker nhận message
springdoc:
  swagger-ui:
    enable: true
    tags-sorter: alpha
    path: /swagger-ui.html
jwt:
  signerKey: ${JWT_SIGNER_KEY}
  valid-duration: 3600 #in second
  refreshable-duration: 604800 #in seconds
aws:
  s3:
    access-key: ${AWS_ACCESS_KEY_ID}
    secret-key: ${AWS_SECRET_ACCESS_KEY}
    region: ${AWS_REGION}
    bucket-name: ${AWS_BUCKET_NAME}
    folder-prefix: ${AWS_FOLDER_PREFIX}
    avt-folder-prefix: ${AWS_AVT_FOLDER_PREFIX}
