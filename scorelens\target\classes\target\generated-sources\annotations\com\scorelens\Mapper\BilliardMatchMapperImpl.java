package com.scorelens.Mapper;

import com.scorelens.DTOs.Request.BilliardMatchCreateRequest;
import com.scorelens.DTOs.Request.BilliardMatchUpdateRequest;
import com.scorelens.DTOs.Response.BilliardMatchResponse;
import com.scorelens.Entity.BilliardMatch;
import com.scorelens.Entity.BilliardTable;
import com.scorelens.Entity.Customer;
import com.scorelens.Entity.Mode;
import com.scorelens.Entity.Staff;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T10:17:15+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 24.0.1 (Oracle Corporation)"
)
@Component
public class BilliardMatchMapperImpl implements BilliardMatchMapper {

    @Autowired
    private GameSetMapper gameSetMapper;
    @Autowired
    private TeamMapper teamMapper;

    @Override
    public BilliardMatch toBilliardMatch(BilliardMatchCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        BilliardMatch billiardMatch = new BilliardMatch();

        billiardMatch.setSetUp( request.getSetUp() );
        billiardMatch.setTotalSet( request.getTotalSet() );

        return billiardMatch;
    }

    @Override
    public BilliardMatchResponse toBilliardMatchResponse(BilliardMatch match) {
        if ( match == null ) {
            return null;
        }

        BilliardMatchResponse billiardMatchResponse = new BilliardMatchResponse();

        billiardMatchResponse.setBilliardTableID( matchBillardTableBillardTableID( match ) );
        billiardMatchResponse.setModeID( matchModeModeID( match ) );
        billiardMatchResponse.setByStaff( matchStaffStaffID( match ) );
        billiardMatchResponse.setByCustomer( matchCustomerCustomerID( match ) );
        billiardMatchResponse.setBilliardMatchID( match.getBilliardMatchID() );
        billiardMatchResponse.setSetUp( match.getSetUp() );
        billiardMatchResponse.setWinner( match.getWinner() );
        billiardMatchResponse.setStartTime( match.getStartTime() );
        billiardMatchResponse.setEndTime( match.getEndTime() );
        billiardMatchResponse.setTotalSet( match.getTotalSet() );
        billiardMatchResponse.setStatus( match.getStatus() );
        billiardMatchResponse.setCode( match.getCode() );
        billiardMatchResponse.setSets( gameSetMapper.toSetResponseList( match.getSets() ) );
        billiardMatchResponse.setTeams( teamMapper.toTeamResponseList( match.getTeams() ) );

        return billiardMatchResponse;
    }

    @Override
    public List<BilliardMatchResponse> toBilliardMatchResponses(List<BilliardMatch> matchs) {
        if ( matchs == null ) {
            return null;
        }

        List<BilliardMatchResponse> list = new ArrayList<BilliardMatchResponse>( matchs.size() );
        for ( BilliardMatch billiardMatch : matchs ) {
            list.add( toBilliardMatchResponse( billiardMatch ) );
        }

        return list;
    }

    @Override
    public void updateBilliardMatchFromRequest(BilliardMatchUpdateRequest request, BilliardMatch match) {
        if ( request == null ) {
            return;
        }

        if ( request.getWinner() != null ) {
            match.setWinner( request.getWinner() );
        }
        if ( request.getStatus() != null ) {
            match.setStatus( request.getStatus() );
        }
    }

    private String matchBillardTableBillardTableID(BilliardMatch billiardMatch) {
        BilliardTable billardTable = billiardMatch.getBillardTable();
        if ( billardTable == null ) {
            return null;
        }
        return billardTable.getBillardTableID();
    }

    private Integer matchModeModeID(BilliardMatch billiardMatch) {
        Mode mode = billiardMatch.getMode();
        if ( mode == null ) {
            return null;
        }
        return mode.getModeID();
    }

    private String matchStaffStaffID(BilliardMatch billiardMatch) {
        Staff staff = billiardMatch.getStaff();
        if ( staff == null ) {
            return null;
        }
        return staff.getStaffID();
    }

    private String matchCustomerCustomerID(BilliardMatch billiardMatch) {
        Customer customer = billiardMatch.getCustomer();
        if ( customer == null ) {
            return null;
        }
        return customer.getCustomerID();
    }
}
