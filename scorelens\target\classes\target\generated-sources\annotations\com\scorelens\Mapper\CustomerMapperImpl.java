package com.scorelens.Mapper;

import com.scorelens.DTOs.Request.CustomerCreateRequestDto;
import com.scorelens.DTOs.Request.CustomerUpdateRequestDto;
import com.scorelens.DTOs.Response.CustomerResponseDto;
import com.scorelens.Entity.Customer;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T10:17:15+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 24.0.1 (Oracle Corporation)"
)
@Component
public class CustomerMapperImpl implements CustomerMapper {

    @Override
    public List<CustomerResponseDto> toDtoList(List<Customer> customerList) {
        if ( customerList == null ) {
            return null;
        }

        List<CustomerResponseDto> list = new ArrayList<CustomerResponseDto>( customerList.size() );
        for ( Customer customer : customerList ) {
            list.add( toDto( customer ) );
        }

        return list;
    }

    @Override
    public CustomerResponseDto toDto(Customer customer) {
        if ( customer == null ) {
            return null;
        }

        String customerID = null;
        String name = null;
        String email = null;
        String phoneNumber = null;
        LocalDate dob = null;
        LocalDate createAt = null;
        LocalDate updateAt = null;
        String type = null;
        String status = null;
        String imageUrl = null;

        customerID = customer.getCustomerID();
        name = customer.getName();
        email = customer.getEmail();
        phoneNumber = customer.getPhoneNumber();
        dob = customer.getDob();
        createAt = customer.getCreateAt();
        updateAt = customer.getUpdateAt();
        type = customer.getType();
        if ( customer.getStatus() != null ) {
            status = customer.getStatus().name();
        }
        imageUrl = customer.getImageUrl();

        CustomerResponseDto customerResponseDto = new CustomerResponseDto( customerID, name, email, phoneNumber, dob, createAt, updateAt, type, status, imageUrl );

        return customerResponseDto;
    }

    @Override
    public Customer toEntity(CustomerCreateRequestDto customerCreateRequestDto) {
        if ( customerCreateRequestDto == null ) {
            return null;
        }

        Customer customer = new Customer();

        customer.setName( customerCreateRequestDto.getName() );
        customer.setEmail( customerCreateRequestDto.getEmail() );
        customer.setPhoneNumber( customerCreateRequestDto.getPhoneNumber() );
        customer.setPassword( customerCreateRequestDto.getPassword() );
        customer.setDob( customerCreateRequestDto.getDob() );

        return customer;
    }

    @Override
    public void updateEntity(Customer customer, CustomerCreateRequestDto customerCreateRequestDto) {
        if ( customerCreateRequestDto == null ) {
            return;
        }

        if ( customerCreateRequestDto.getName() != null ) {
            customer.setName( customerCreateRequestDto.getName() );
        }
        if ( customerCreateRequestDto.getEmail() != null ) {
            customer.setEmail( customerCreateRequestDto.getEmail() );
        }
        if ( customerCreateRequestDto.getPhoneNumber() != null ) {
            customer.setPhoneNumber( customerCreateRequestDto.getPhoneNumber() );
        }
        if ( customerCreateRequestDto.getPassword() != null ) {
            customer.setPassword( customerCreateRequestDto.getPassword() );
        }
        if ( customerCreateRequestDto.getDob() != null ) {
            customer.setDob( customerCreateRequestDto.getDob() );
        }
    }

    @Override
    public void updateEntity(Customer customer, CustomerUpdateRequestDto customerUpdateRequestDto) {
        if ( customerUpdateRequestDto == null ) {
            return;
        }

        if ( customerUpdateRequestDto.getName() != null ) {
            customer.setName( customerUpdateRequestDto.getName() );
        }
        if ( customerUpdateRequestDto.getEmail() != null ) {
            customer.setEmail( customerUpdateRequestDto.getEmail() );
        }
        if ( customerUpdateRequestDto.getPhoneNumber() != null ) {
            customer.setPhoneNumber( customerUpdateRequestDto.getPhoneNumber() );
        }
        if ( customerUpdateRequestDto.getDob() != null ) {
            customer.setDob( customerUpdateRequestDto.getDob() );
        }
        if ( customerUpdateRequestDto.getImageUrl() != null ) {
            customer.setImageUrl( customerUpdateRequestDto.getImageUrl() );
        }
    }
}
