package com.scorelens.Mapper;

import com.scorelens.DTOs.Request.ModeRequest;
import com.scorelens.DTOs.Response.ModeResponse;
import com.scorelens.Entity.Mode;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T10:17:15+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 24.0.1 (Oracle Corporation)"
)
@Component
public class ModeMapperImpl implements ModeMapper {

    @Override
    public Mode toMode(ModeRequest request) {
        if ( request == null ) {
            return null;
        }

        Mode mode = new Mode();

        mode.setName( request.getName() );
        mode.setDescription( request.getDescription() );
        mode.setActive( request.isActive() );

        return mode;
    }

    @Override
    public ModeResponse toResponse(Mode mode) {
        if ( mode == null ) {
            return null;
        }

        ModeResponse modeResponse = new ModeResponse();

        modeResponse.setModeID( mode.getModeID() );
        modeResponse.setName( mode.getName() );
        modeResponse.setDescription( mode.getDescription() );
        modeResponse.setActive( mode.isActive() );

        return modeResponse;
    }

    @Override
    public List<ModeResponse> toResponses(List<Mode> mode) {
        if ( mode == null ) {
            return null;
        }

        List<ModeResponse> list = new ArrayList<ModeResponse>( mode.size() );
        for ( Mode mode1 : mode ) {
            list.add( toResponse( mode1 ) );
        }

        return list;
    }
}
