package com.scorelens.Mapper;

import com.scorelens.DTOs.Response.StaffBasicResponse;
import com.scorelens.Entity.Staff;
import com.scorelens.Enums.StatusType;
import java.time.LocalDate;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T10:17:15+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 24.0.1 (Oracle Corporation)"
)
@Component
public class StaffBasicMapperImpl implements StaffBasicMapper {

    @Override
    public StaffBasicResponse toStaffBasicResponse(Staff staff) {
        if ( staff == null ) {
            return null;
        }

        String staffID = null;
        String name = null;
        String email = null;
        String phoneNumber = null;
        LocalDate dob = null;
        String address = null;
        StatusType status = null;

        staffID = staff.getStaffID();
        name = staff.getName();
        email = staff.getEmail();
        phoneNumber = staff.getPhoneNumber();
        dob = staff.getDob();
        address = staff.getAddress();
        status = staff.getStatus();

        StaffBasicResponse staffBasicResponse = new StaffBasicResponse( staffID, name, email, phoneNumber, dob, address, status );

        return staffBasicResponse;
    }
}
