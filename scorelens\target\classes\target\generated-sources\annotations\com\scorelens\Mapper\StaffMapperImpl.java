package com.scorelens.Mapper;

import com.scorelens.DTOs.Request.StaffCreateRequestDto;
import com.scorelens.DTOs.Request.StaffUpdateRequestDto;
import com.scorelens.DTOs.Response.RoleResponse;
import com.scorelens.DTOs.Response.StaffBasicResponse;
import com.scorelens.DTOs.Response.StaffResponseDto;
import com.scorelens.DTOs.Response.StoreBasicResponse;
import com.scorelens.Entity.Staff;
import com.scorelens.Enums.StatusType;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T10:17:15+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 24.0.1 (Oracle Corporation)"
)
@Component
public class StaffMapperImpl implements StaffMapper {

    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private StoreBasicMapper storeBasicMapper;
    @Autowired
    private StaffBasicMapper staffBasicMapper;

    @Override
    public StaffResponseDto toDto(Staff staff) {
        if ( staff == null ) {
            return null;
        }

        StaffBasicResponse manager = null;
        Set<RoleResponse> roles = null;
        StoreBasicResponse store = null;
        String staffID = null;
        String name = null;
        String email = null;
        String phoneNumber = null;
        LocalDate dob = null;
        String address = null;
        LocalDate createAt = null;
        LocalDate updateAt = null;
        StatusType status = null;
        String imageUrl = null;

        manager = staffBasicMapper.toStaffBasicResponse( staff.getManager() );
        roles = roleMapper.toRoleResponseSet( staff.getRoles() );
        store = storeBasicMapper.toStoreBasicResponse( staff.getStore() );
        staffID = staff.getStaffID();
        name = staff.getName();
        email = staff.getEmail();
        phoneNumber = staff.getPhoneNumber();
        dob = staff.getDob();
        address = staff.getAddress();
        createAt = staff.getCreateAt();
        updateAt = staff.getUpdateAt();
        status = staff.getStatus();
        imageUrl = staff.getImageUrl();

        StaffResponseDto staffResponseDto = new StaffResponseDto( staffID, name, email, phoneNumber, dob, address, roles, createAt, updateAt, status, manager, store, imageUrl );

        return staffResponseDto;
    }

    @Override
    public List<StaffResponseDto> toDto(List<Staff> staffList) {
        if ( staffList == null ) {
            return null;
        }

        List<StaffResponseDto> list = new ArrayList<StaffResponseDto>( staffList.size() );
        for ( Staff staff : staffList ) {
            list.add( toDto( staff ) );
        }

        return list;
    }

    @Override
    public Staff toEntity(StaffCreateRequestDto staffRequestDto) {
        if ( staffRequestDto == null ) {
            return null;
        }

        Staff.StaffBuilder staff = Staff.builder();

        staff.name( staffRequestDto.getName() );
        staff.email( staffRequestDto.getEmail() );
        staff.phoneNumber( staffRequestDto.getPhoneNumber() );
        staff.dob( staffRequestDto.getDob() );
        staff.address( staffRequestDto.getAddress() );
        staff.password( staffRequestDto.getPassword() );

        return staff.build();
    }

    @Override
    public void updateStaff(Staff staff, StaffUpdateRequestDto request) {
        if ( request == null ) {
            return;
        }

        staff.setName( request.getName() );
        staff.setEmail( request.getEmail() );
        staff.setPhoneNumber( request.getPhoneNumber() );
        staff.setDob( request.getDob() );
        staff.setAddress( request.getAddress() );
        staff.setStatus( request.getStatus() );
    }
}
