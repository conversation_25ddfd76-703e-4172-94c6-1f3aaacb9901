package com.scorelens.Mapper;

import com.scorelens.DTOs.Request.StoreRequest;
import com.scorelens.DTOs.Response.BilliardTableResponse;
import com.scorelens.DTOs.Response.StoreResponse;
import com.scorelens.Entity.BilliardTable;
import com.scorelens.Entity.Store;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T10:17:15+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 24.0.1 (Oracle Corporation)"
)
@Component
public class StoreMapperImpl implements StoreMapper {

    @Override
    public List<StoreResponse> toStoreResponseList(List<Store> storeList) {
        if ( storeList == null ) {
            return null;
        }

        List<StoreResponse> list = new ArrayList<StoreResponse>( storeList.size() );
        for ( Store store : storeList ) {
            list.add( toStoreResponse( store ) );
        }

        return list;
    }

    @Override
    public Store toStore(StoreRequest storeRequest) {
        if ( storeRequest == null ) {
            return null;
        }

        Store store = new Store();

        store.setName( storeRequest.getName() );
        store.setAddress( storeRequest.getAddress() );
        store.setStatus( storeRequest.getStatus() );
        store.setDescription( storeRequest.getDescription() );

        return store;
    }

    @Override
    public StoreResponse toStoreResponse(Store store) {
        if ( store == null ) {
            return null;
        }

        StoreResponse storeResponse = new StoreResponse();

        storeResponse.setStoreID( store.getStoreID() );
        storeResponse.setName( store.getName() );
        storeResponse.setAddress( store.getAddress() );
        storeResponse.setStatus( store.getStatus() );
        storeResponse.setDescription( store.getDescription() );
        storeResponse.setBilliardTables( billiardTableListToBilliardTableResponseList( store.getBilliardTables() ) );

        return storeResponse;
    }

    @Override
    public void updateStore(Store store, StoreRequest storeRequest) {
        if ( storeRequest == null ) {
            return;
        }

        if ( storeRequest.getName() != null ) {
            store.setName( storeRequest.getName() );
        }
        if ( storeRequest.getAddress() != null ) {
            store.setAddress( storeRequest.getAddress() );
        }
        if ( storeRequest.getStatus() != null ) {
            store.setStatus( storeRequest.getStatus() );
        }
        if ( storeRequest.getDescription() != null ) {
            store.setDescription( storeRequest.getDescription() );
        }
    }

    protected BilliardTableResponse billiardTableToBilliardTableResponse(BilliardTable billiardTable) {
        if ( billiardTable == null ) {
            return null;
        }

        BilliardTableResponse billiardTableResponse = new BilliardTableResponse();

        billiardTableResponse.setBillardTableID( billiardTable.getBillardTableID() );
        billiardTableResponse.setTableCode( billiardTable.getTableCode() );
        billiardTableResponse.setTableType( billiardTable.getTableType() );
        billiardTableResponse.setName( billiardTable.getName() );
        billiardTableResponse.setDescription( billiardTable.getDescription() );
        billiardTableResponse.setStatus( billiardTable.getStatus() );
        billiardTableResponse.setQrCode( billiardTable.getQrCode() );
        billiardTableResponse.setCameraUrl( billiardTable.getCameraUrl() );
        billiardTableResponse.setActive( billiardTable.isActive() );

        return billiardTableResponse;
    }

    protected List<BilliardTableResponse> billiardTableListToBilliardTableResponseList(List<BilliardTable> list) {
        if ( list == null ) {
            return null;
        }

        List<BilliardTableResponse> list1 = new ArrayList<BilliardTableResponse>( list.size() );
        for ( BilliardTable billiardTable : list ) {
            list1.add( billiardTableToBilliardTableResponse( billiardTable ) );
        }

        return list1;
    }
}
