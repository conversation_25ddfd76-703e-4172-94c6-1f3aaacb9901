package com.scorelens.Mapper;

import com.scorelens.DTOs.Response.TeamSetResponse;
import com.scorelens.Entity.GameSet;
import com.scorelens.Entity.Team;
import com.scorelens.Entity.TeamSet;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T10:17:15+0700",
    comments = "version: 1.6.3, compiler: javac, environment: Java 24.0.1 (Oracle Corporation)"
)
@Component
public class TeamSetMapperImpl implements TeamSetMapper {

    @Override
    public TeamSetResponse toResponse(TeamSet teamSet) {
        if ( teamSet == null ) {
            return null;
        }

        TeamSetResponse teamSetResponse = new TeamSetResponse();

        Integer teamID = teamSetTeamTeamID( teamSet );
        if ( teamID != null ) {
            teamSetResponse.setTeamID( teamID );
        }
        teamSetResponse.setGameSetID( teamSetGameSetGameSetID( teamSet ) );
        teamSetResponse.setTeamSetID( teamSet.getTeamSetID() );
        if ( teamSet.getTotalScore() != null ) {
            teamSetResponse.setTotalScore( teamSet.getTotalScore() );
        }

        return teamSetResponse;
    }

    @Override
    public List<TeamSetResponse> toResponseList(List<TeamSet> tss) {
        if ( tss == null ) {
            return null;
        }

        List<TeamSetResponse> list = new ArrayList<TeamSetResponse>( tss.size() );
        for ( TeamSet teamSet : tss ) {
            list.add( toResponse( teamSet ) );
        }

        return list;
    }

    private Integer teamSetTeamTeamID(TeamSet teamSet) {
        Team team = teamSet.getTeam();
        if ( team == null ) {
            return null;
        }
        return team.getTeamID();
    }

    private int teamSetGameSetGameSetID(TeamSet teamSet) {
        GameSet gameSet = teamSet.getGameSet();
        if ( gameSet == null ) {
            return 0;
        }
        return gameSet.getGameSetID();
    }
}
