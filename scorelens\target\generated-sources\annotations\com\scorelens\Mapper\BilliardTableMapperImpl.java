package com.scorelens.Mapper;

import com.scorelens.DTOs.Request.BilliardTableRequest;
import com.scorelens.DTOs.Response.BilliardTableResponse;
import com.scorelens.Entity.BilliardTable;
import com.scorelens.Entity.Store;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T10:22:21+0700",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class BilliardTableMapperImpl implements BilliardTableMapper {

    @Override
    public BilliardTableResponse toBilliardTableResponse(BilliardTable billiardTable) {
        if ( billiardTable == null ) {
            return null;
        }

        BilliardTableResponse billiardTableResponse = new BilliardTableResponse();

        billiardTableResponse.setStoreID( billiardTableStoreStoreID( billiardTable ) );
        billiardTableResponse.setBillardTableID( billiardTable.getBillardTableID() );
        billiardTableResponse.setTableCode( billiardTable.getTableCode() );
        billiardTableResponse.setTableType( billiardTable.getTableType() );
        billiardTableResponse.setName( billiardTable.getName() );
        billiardTableResponse.setDescription( billiardTable.getDescription() );
        billiardTableResponse.setStatus( billiardTable.getStatus() );
        billiardTableResponse.setQrCode( billiardTable.getQrCode() );
        billiardTableResponse.setCameraUrl( billiardTable.getCameraUrl() );
        billiardTableResponse.setActive( billiardTable.isActive() );

        return billiardTableResponse;
    }

    @Override
    public BilliardTable toBilliardTable(BilliardTableResponse billiardTableResponse) {
        if ( billiardTableResponse == null ) {
            return null;
        }

        BilliardTable billiardTable = new BilliardTable();

        billiardTable.setBillardTableID( billiardTableResponse.getBillardTableID() );
        billiardTable.setTableCode( billiardTableResponse.getTableCode() );
        billiardTable.setTableType( billiardTableResponse.getTableType() );
        billiardTable.setName( billiardTableResponse.getName() );
        billiardTable.setDescription( billiardTableResponse.getDescription() );
        billiardTable.setStatus( billiardTableResponse.getStatus() );
        billiardTable.setQrCode( billiardTableResponse.getQrCode() );
        billiardTable.setCameraUrl( billiardTableResponse.getCameraUrl() );
        billiardTable.setActive( billiardTableResponse.isActive() );

        return billiardTable;
    }

    @Override
    public List<BilliardTableResponse> toBilliardTableResponsesList(List<BilliardTable> billiardTables) {
        if ( billiardTables == null ) {
            return null;
        }

        List<BilliardTableResponse> list = new ArrayList<BilliardTableResponse>( billiardTables.size() );
        for ( BilliardTable billiardTable : billiardTables ) {
            list.add( toBilliardTableResponse( billiardTable ) );
        }

        return list;
    }

    @Override
    public BilliardTable toBilliardTable(BilliardTableRequest billiardTableRequest) {
        if ( billiardTableRequest == null ) {
            return null;
        }

        BilliardTable billiardTable = new BilliardTable();

        billiardTable.setTableType( billiardTableRequest.getTableType() );
        billiardTable.setName( billiardTableRequest.getName() );
        billiardTable.setDescription( billiardTableRequest.getDescription() );
        billiardTable.setStatus( billiardTableRequest.getStatus() );
        billiardTable.setCameraUrl( billiardTableRequest.getCameraUrl() );
        billiardTable.setActive( billiardTableRequest.isActive() );

        return billiardTable;
    }

    @Override
    public void updateBilliardTable(BilliardTable billiardTable, BilliardTableRequest billiardTableRequest) {
        if ( billiardTableRequest == null ) {
            return;
        }

        if ( billiardTableRequest.getTableType() != null ) {
            billiardTable.setTableType( billiardTableRequest.getTableType() );
        }
        if ( billiardTableRequest.getName() != null ) {
            billiardTable.setName( billiardTableRequest.getName() );
        }
        if ( billiardTableRequest.getDescription() != null ) {
            billiardTable.setDescription( billiardTableRequest.getDescription() );
        }
        if ( billiardTableRequest.getStatus() != null ) {
            billiardTable.setStatus( billiardTableRequest.getStatus() );
        }
        if ( billiardTableRequest.getCameraUrl() != null ) {
            billiardTable.setCameraUrl( billiardTableRequest.getCameraUrl() );
        }
        billiardTable.setActive( billiardTableRequest.isActive() );
    }

    private String billiardTableStoreStoreID(BilliardTable billiardTable) {
        if ( billiardTable == null ) {
            return null;
        }
        Store store = billiardTable.getStore();
        if ( store == null ) {
            return null;
        }
        String storeID = store.getStoreID();
        if ( storeID == null ) {
            return null;
        }
        return storeID;
    }
}
