package com.scorelens.Mapper;

import com.scorelens.DTOs.Request.EventRequest;
import com.scorelens.DTOs.Response.EventResponse;
import com.scorelens.Entity.Event;
import com.scorelens.Entity.GameSet;
import com.scorelens.Entity.Player;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T10:22:20+0700",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class EventMapperImpl implements EventMapper {

    @Override
    public EventResponse toEventResponse(Event event) {
        if ( event == null ) {
            return null;
        }

        EventResponse.EventResponseBuilder eventResponse = EventResponse.builder();

        eventResponse.playerID( eventPlayerPlayerID( event ) );
        eventResponse.gameSetID( eventGameSetGameSetID( event ) );
        eventResponse.eventID( event.getEventID() );
        eventResponse.scoreValue( event.isScoreValue() );
        eventResponse.timeStamp( event.getTimeStamp() );
        eventResponse.message( event.getMessage() );
        eventResponse.sceneUrl( event.getSceneUrl() );

        return eventResponse.build();
    }

    @Override
    public Event toEvent(EventResponse eventResponse) {
        if ( eventResponse == null ) {
            return null;
        }

        Event event = new Event();

        event.setPlayer( eventResponseToPlayer( eventResponse ) );
        event.setGameSet( eventResponseToGameSet( eventResponse ) );
        event.setEventID( eventResponse.getEventID() );
        event.setScoreValue( eventResponse.isScoreValue() );
        event.setFoul( eventResponse.isFoul() );
        event.setUncertain( eventResponse.isUncertain() );
        event.setTimeStamp( eventResponse.getTimeStamp() );
        event.setMessage( eventResponse.getMessage() );
        event.setSceneUrl( eventResponse.getSceneUrl() );

        return event;
    }

    @Override
    public Event toEventRequest(EventRequest eventRequest) {
        if ( eventRequest == null ) {
            return null;
        }

        Event event = new Event();

        event.setPlayer( eventRequestToPlayer( eventRequest ) );
        event.setGameSet( eventRequestToGameSet( eventRequest ) );
        event.setScoreValue( eventRequest.isScoreValue() );
        event.setFoul( eventRequest.isFoul() );
        event.setUncertain( eventRequest.isUncertain() );
        event.setMessage( eventRequest.getMessage() );
        event.setSceneUrl( eventRequest.getSceneUrl() );

        return event;
    }

    @Override
    public List<EventResponse> toEventResponses(List<Event> events) {
        if ( events == null ) {
            return null;
        }

        List<EventResponse> list = new ArrayList<EventResponse>( events.size() );
        for ( Event event : events ) {
            list.add( toEventResponse( event ) );
        }

        return list;
    }

    private int eventPlayerPlayerID(Event event) {
        if ( event == null ) {
            return 0;
        }
        Player player = event.getPlayer();
        if ( player == null ) {
            return 0;
        }
        int playerID = player.getPlayerID();
        return playerID;
    }

    private int eventGameSetGameSetID(Event event) {
        if ( event == null ) {
            return 0;
        }
        GameSet gameSet = event.getGameSet();
        if ( gameSet == null ) {
            return 0;
        }
        int gameSetID = gameSet.getGameSetID();
        return gameSetID;
    }

    protected Player eventResponseToPlayer(EventResponse eventResponse) {
        if ( eventResponse == null ) {
            return null;
        }

        Player player = new Player();

        player.setPlayerID( eventResponse.getPlayerID() );

        return player;
    }

    protected GameSet eventResponseToGameSet(EventResponse eventResponse) {
        if ( eventResponse == null ) {
            return null;
        }

        GameSet gameSet = new GameSet();

        gameSet.setGameSetID( eventResponse.getGameSetID() );

        return gameSet;
    }

    protected Player eventRequestToPlayer(EventRequest eventRequest) {
        if ( eventRequest == null ) {
            return null;
        }

        Player player = new Player();

        player.setPlayerID( eventRequest.getPlayerID() );

        return player;
    }

    protected GameSet eventRequestToGameSet(EventRequest eventRequest) {
        if ( eventRequest == null ) {
            return null;
        }

        GameSet gameSet = new GameSet();

        gameSet.setGameSetID( eventRequest.getGameSetID() );

        return gameSet;
    }
}
