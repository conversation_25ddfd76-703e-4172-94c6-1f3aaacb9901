package com.scorelens.Mapper;

import com.scorelens.DTOs.Request.GameSetCreateRequest;
import com.scorelens.DTOs.Request.GameSetUpdateRequest;
import com.scorelens.DTOs.Response.GameSetResponse;
import com.scorelens.Entity.BilliardMatch;
import com.scorelens.Entity.GameSet;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T10:22:21+0700",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class GameSetMapperImpl implements GameSetMapper {

    @Autowired
    private TeamSetMapper teamSetMapper;

    @Override
    public GameSet toGameSet(GameSetCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        GameSet gameSet = new GameSet();

        if ( request.getRaceTo() != null ) {
            gameSet.setRaceTo( request.getRaceTo() );
        }

        return gameSet;
    }

    @Override
    public GameSetResponse toGameSetResponse(GameSet gameSet) {
        if ( gameSet == null ) {
            return null;
        }

        GameSetResponse gameSetResponse = new GameSetResponse();

        gameSetResponse.setBilliardMatchID( gameSetBilliardMatchBilliardMatchID( gameSet ) );
        gameSetResponse.setGameSetID( gameSet.getGameSetID() );
        gameSetResponse.setGameSetNo( gameSet.getGameSetNo() );
        gameSetResponse.setRaceTo( gameSet.getRaceTo() );
        gameSetResponse.setWinner( gameSet.getWinner() );
        gameSetResponse.setStartTime( gameSet.getStartTime() );
        gameSetResponse.setEndTime( gameSet.getEndTime() );
        gameSetResponse.setStatus( gameSet.getStatus() );
        gameSetResponse.setTss( teamSetMapper.toResponseList( gameSet.getTss() ) );

        return gameSetResponse;
    }

    @Override
    public List<GameSetResponse> toSetResponseList(List<GameSet> sets) {
        if ( sets == null ) {
            return null;
        }

        List<GameSetResponse> list = new ArrayList<GameSetResponse>( sets.size() );
        for ( GameSet gameSet : sets ) {
            list.add( toGameSetResponse( gameSet ) );
        }

        return list;
    }

    @Override
    public void updateGameSetFromRequest(GameSetUpdateRequest request, GameSet gameSet) {
        if ( request == null ) {
            return;
        }

        if ( request.getRaceTo() != null ) {
            gameSet.setRaceTo( request.getRaceTo() );
        }
        if ( request.getWinner() != null ) {
            gameSet.setWinner( request.getWinner() );
        }
        if ( request.getStatus() != null ) {
            gameSet.setStatus( request.getStatus() );
        }
    }

    private Integer gameSetBilliardMatchBilliardMatchID(GameSet gameSet) {
        if ( gameSet == null ) {
            return null;
        }
        BilliardMatch billiardMatch = gameSet.getBilliardMatch();
        if ( billiardMatch == null ) {
            return null;
        }
        Integer billiardMatchID = billiardMatch.getBilliardMatchID();
        if ( billiardMatchID == null ) {
            return null;
        }
        return billiardMatchID;
    }
}
