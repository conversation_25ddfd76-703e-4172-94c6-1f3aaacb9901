package com.scorelens.Mapper;

import com.scorelens.DTOs.Request.NotificationRequest;
import com.scorelens.DTOs.Response.NotificationResponse;
import com.scorelens.Entity.Notification;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T10:22:20+0700",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NotificationMapperImpl implements NotificationMapper {

    @Override
    public Notification toNotiRequest(NotificationRequest notificationRequest) {
        if ( notificationRequest == null ) {
            return null;
        }

        Notification notification = new Notification();

        notification.setMessage( notificationRequest.getMessage() );
        notification.setType( notificationRequest.getType() );

        return notification;
    }

    @Override
    public NotificationResponse toNotiResponse(Notification notification) {
        if ( notification == null ) {
            return null;
        }

        NotificationResponse notificationResponse = new NotificationResponse();

        notificationResponse.setNotificationID( notification.getNotificationID() );
        notificationResponse.setMessage( notification.getMessage() );
        notificationResponse.setType( notification.getType() );
        notificationResponse.setCreateAt( notification.getCreateAt() );

        return notificationResponse;
    }

    @Override
    public List<NotificationResponse> toNotiResponseList(List<Notification> notifications) {
        if ( notifications == null ) {
            return null;
        }

        List<NotificationResponse> list = new ArrayList<NotificationResponse>( notifications.size() );
        for ( Notification notification : notifications ) {
            list.add( toNotiResponse( notification ) );
        }

        return list;
    }
}
