package com.scorelens.Mapper;

import com.scorelens.DTOs.Request.PlayerCreateRequest;
import com.scorelens.DTOs.Request.PlayerUpdateRequest;
import com.scorelens.DTOs.Response.PlayerResponse;
import com.scorelens.Entity.Player;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T10:22:21+0700",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class PlayerMapperImpl implements PlayerMapper {

    @Override
    public Player toPlayer(PlayerCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        Player player = new Player();

        player.setName( request.getName() );

        return player;
    }

    @Override
    public PlayerResponse toDto(Player player) {
        if ( player == null ) {
            return null;
        }

        PlayerResponse playerResponse = new PlayerResponse();

        playerResponse.setPlayerID( player.getPlayerID() );
        playerResponse.setName( player.getName() );
        playerResponse.setTotalScore( player.getTotalScore() );
        playerResponse.setStatus( player.getStatus() );
        playerResponse.setCreateAt( player.getCreateAt() );

        playerResponse.setTeamID( player.getTeam() != null ? player.getTeam().getTeamID() : null );
        playerResponse.setCustomerID( player.getCustomer() != null ? player.getCustomer().getCustomerID() : null );

        return playerResponse;
    }

    @Override
    public List<PlayerResponse> toDto(List<Player> players) {
        if ( players == null ) {
            return null;
        }

        List<PlayerResponse> list = new ArrayList<PlayerResponse>( players.size() );
        for ( Player player : players ) {
            list.add( toDto( player ) );
        }

        return list;
    }

    @Override
    public void update(Player player, PlayerUpdateRequest request) {
        if ( request == null ) {
            return;
        }

        if ( request.getName() != null ) {
            player.setName( request.getName() );
        }
        player.setTotalScore( request.getTotalScore() );
        if ( request.getStatus() != null ) {
            player.setStatus( request.getStatus() );
        }
    }
}
