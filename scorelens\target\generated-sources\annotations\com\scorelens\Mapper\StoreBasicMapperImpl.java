package com.scorelens.Mapper;

import com.scorelens.DTOs.Response.StoreBasicResponse;
import com.scorelens.Entity.Store;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T10:22:21+0700",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class StoreBasicMapperImpl implements StoreBasicMapper {

    @Override
    public StoreBasicResponse toStoreBasicResponse(Store store) {
        if ( store == null ) {
            return null;
        }

        String storeID = null;
        String name = null;
        String address = null;
        String status = null;
        String description = null;

        storeID = store.getStoreID();
        name = store.getName();
        address = store.getAddress();
        status = store.getStatus();
        description = store.getDescription();

        StoreBasicResponse storeBasicResponse = new StoreBasicResponse( storeID, name, address, status, description );

        return storeBasicResponse;
    }
}
