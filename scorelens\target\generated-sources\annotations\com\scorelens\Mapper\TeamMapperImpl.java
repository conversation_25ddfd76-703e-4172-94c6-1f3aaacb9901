package com.scorelens.Mapper;

import com.scorelens.DTOs.Request.TeamCreateRequest;
import com.scorelens.DTOs.Request.TeamUpdateRequest;
import com.scorelens.DTOs.Response.TeamResponse;
import com.scorelens.Entity.BilliardMatch;
import com.scorelens.Entity.Team;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T10:22:21+0700",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class TeamMapperImpl implements TeamMapper {

    @Autowired
    private PlayerMapper playerMapper;

    @Override
    public Team toTeam(TeamCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        Team team = new Team();

        team.setName( request.getName() );
        team.setTotalMember( request.getTotalMember() );

        return team;
    }

    @Override
    public TeamResponse toTeamResponse(Team team) {
        if ( team == null ) {
            return null;
        }

        TeamResponse teamResponse = new TeamResponse();

        teamResponse.setBilliardMatchID( teamBilliardMatchBilliardMatchID( team ) );
        if ( team.getTeamID() != null ) {
            teamResponse.setTeamID( team.getTeamID() );
        }
        teamResponse.setName( team.getName() );
        if ( team.getTotalMember() != null ) {
            teamResponse.setTotalMember( team.getTotalMember() );
        }
        if ( team.getTotalScore() != null ) {
            teamResponse.setTotalScore( team.getTotalScore() );
        }
        teamResponse.setCreateAt( team.getCreateAt() );
        teamResponse.setStatus( team.getStatus() );
        teamResponse.setPlayers( playerMapper.toDto( team.getPlayers() ) );

        return teamResponse;
    }

    @Override
    public List<TeamResponse> toTeamResponseList(List<Team> teams) {
        if ( teams == null ) {
            return null;
        }

        List<TeamResponse> list = new ArrayList<TeamResponse>( teams.size() );
        for ( Team team : teams ) {
            list.add( toTeamResponse( team ) );
        }

        return list;
    }

    @Override
    public void updateTeam(TeamUpdateRequest request, Team team) {
        if ( request == null ) {
            return;
        }

        if ( request.getName() != null ) {
            team.setName( request.getName() );
        }
        if ( request.getTotalMember() != null ) {
            team.setTotalMember( request.getTotalMember() );
        }
        if ( request.getTotalScore() != null ) {
            team.setTotalScore( request.getTotalScore() );
        }
        if ( request.getStatus() != null ) {
            team.setStatus( request.getStatus() );
        }
    }

    private Integer teamBilliardMatchBilliardMatchID(Team team) {
        if ( team == null ) {
            return null;
        }
        BilliardMatch billiardMatch = team.getBilliardMatch();
        if ( billiardMatch == null ) {
            return null;
        }
        Integer billiardMatchID = billiardMatch.getBilliardMatchID();
        if ( billiardMatchID == null ) {
            return null;
        }
        return billiardMatchID;
    }
}
